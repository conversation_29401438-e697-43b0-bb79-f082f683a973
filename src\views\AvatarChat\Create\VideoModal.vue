<script setup lang="ts">
  import { ref, watch } from 'vue';
  import axios from 'axios';

  import {
    PlusOutlined,
    LoadingOutlined,
    RightOutlined,
    InfoCircleOutlined,
    DeleteOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { getLocalItem } from '@/utils/common';
  import { getModeDigitals } from '@/api/avatarChat';
  import { getDigitalHumansPreview, generateDigitalHumans } from '@/api/videoGeneratedDigitalHumans';
  import { getEnv } from '@/utils';

  const { VITE_APP_AVATAR_URL } = getEnv();
  import Step from './Step.vue';
  import Icon from '@/components/Icon/index.vue';
  import { UploadVideoTips, AvatarErrorPhotos } from './constant';
  import type { DigitalHumanItemProps } from './constant';

  // import { Loading } from '@/components';
  import DefaultDigitalPreviewImg from '@/assets/image/base/pictures/digital-preview-icon.png';
  import DigitalHumanSpeakersModal from '../Speakers/index.vue';
  import UserRightsNotice from './UserRightsNotice.vue';

  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  const props = defineProps<{
    visible?: boolean;
    handleCloseModal?: () => void;
  }>();

  const emit = defineEmits(['close']);

  const isOpenModal = ref(false);
  const digitalHumanLoading = ref(false);
  const loading = ref(false);
  const pageLoading = ref(false);
  const trainMode = ref('fast');
  const digitalList = ref<{ url: string; gender: string }[]>([{ url: '', gender: '' }]);
  const selectedDigitalMan = ref({ index: 0, url: '', gender: '' });
  const uploadAvatarUrl = ref('');
  type UploadVideoResult = { url: string; first_10s_url: string; remaining_url: string };
  const uploadResult = ref<UploadVideoResult | null>(null);
  const preViewUrl = ref('');
  const name = ref('');
  const isNameEntered = ref(true);
  const isAvatarUploaded = ref(true);

  // 上传进度相关状态
  const uploadProgress = ref(0);
  const isUploading = ref(false);
  const uploadCancelToken = ref<any>(null);

  // 添加选中声音的状态
  const selectedSpeaker = ref<{ name: string; description: string } | null>(null);

  const speakersModalRef = ref();

  // 版权校验相关状态
  const isAgreedToTerms = ref(false);
  const userRightsNoticeRef = ref();

  const resetState = () => {
    trainMode.value = 'fast';
    uploadAvatarUrl.value = '';
    uploadResult.value = null;
    preViewUrl.value = '';
    selectedDigitalMan.value = { index: 0, url: '', gender: '' };
    isNameEntered.value = true;
    isAvatarUploaded.value = true;
    selectedSpeaker.value = null;
    isAgreedToTerms.value = false;
    // 重置上传进度状态
    uploadProgress.value = 0;
    isUploading.value = false;
    uploadCancelToken.value = null;
    queryDigitalHumanList();
  };

  const openModal = () => {
    resetState();
    isOpenModal.value = true;
  };

  defineExpose({
    openModal,
  });

  watch(
    () => props.visible,
    (val) => {
      if (val) {
        resetState();
        isOpenModal.value = true;
      } else {
        isOpenModal.value = false;
      }
    },
    { immediate: true },
  );

  const handlePreview = () => {
    console.log('preview');
    isAvatarUploaded.value = Boolean(uploadAvatarUrl.value);
    if (!uploadAvatarUrl.value) {
      return;
    }

    if (!uploadResult.value) {
      message.error('请先上传视频');
      return;
    }

    // 每次点击都要设置 loading
    pageLoading.value = true;
    preViewUrl.value = '';
    getDigitalHumansPreview({
      video_url: uploadResult.value.url,
      first_10s_url: uploadResult.value.first_10s_url,
      remaining_url: uploadResult.value.remaining_url,
      default_speaker: selectedSpeaker.value?.name,
      // category: trainMode.value,
      // source_path: uploadAvatarUrl.value,
      // target_path: selectedDigitalMan.value.url,
      // user_id: userId,
    })
      .then((data: string) => {
        preViewUrl.value = data.preview_url as string;
      })
      .catch(() => {
        pageLoading.value = false;
        console.log('预览失败');
        // message.error('预览失败');
      })
      .finally(() => {
        pageLoading.value = false;
      });
  };

  const handleTrain = async () => {
    isNameEntered.value = Boolean(name.value);
    if (!name.value) {
      return;
    }

    // 检查是否勾选了协议
    if (!isAgreedToTerms.value) {
      userRightsNoticeRef.value?.openModal();
      return;
    }

    pageLoading.value = true;
    // const isCartoon = selectedDigitalMan.value.index === digitalList.value.length - 1;
    generateDigitalHumans({
      name: name.value,
      video_url: uploadResult.value.url,
      first_10s_url: uploadResult.value.first_10s_url,
      remaining_url: uploadResult.value.remaining_url,
      preview_url: preViewUrl.value,
      user_id: userId,
      default_speaker: selectedSpeaker.value?.name,
      // category: trainMode.value,
      // image_url: preViewUrl.value,
      // source_path: uploadAvatarUrl.value,
      // is_cartoon: isCartoon,
      // target_path: isCartoon ? '' : selectedDigitalMan.value.url,
      // gender: isCartoon ? '' : selectedDigitalMan.value.gender,
    })
      .then(() => {
        isOpenModal.value = false;
        emit('close');
        message.success('已经开始训练');
      })
      .catch(() => {
        message.error('训练失败');
      })
      .finally(() => {
        pageLoading.value = false;
      });
  };

  // const handleTrainModeChange = (value: string) => {
  //   trainMode.value = value;
  // };

  const handleDigitalModeChange = (index: number, item: DigitalHumanItemProps) => {
    selectedDigitalMan.value = { index, ...item };
    // console.log(selectedDigitalMan.value);
  };

  const handleNameChange = (value = '') => {
    const newName = value;
    console.log(newName);
    isNameEntered.value = Boolean(newName);
    name.value = newName;
  };

  const handleOk = () => {
    isOpenModal.value = false;
    emit('close');
  };

  const handleCancel = () => {
    isOpenModal.value = false;
    emit('close');
  };

  async function queryDigitalHumanList() {
    digitalHumanLoading.value = true;
    getModeDigitals()
      .then((data: any) => {
        digitalList.value = data;
        selectedDigitalMan.value = { index: 0, ...data[0] };
      })
      .finally(() => {
        digitalHumanLoading.value = false;
      });
  }

  // 校验视频元数据：分辨率/比例、时长、FPS
  const validateVideoMetadata = (file: File) => {
    return new Promise<boolean>((resolve) => {
      const objectUrl = URL.createObjectURL(file);
      const video = document.createElement('video');
      video.preload = 'metadata';
      (video as any).playsInline = true;
      video.muted = true;
      video.src = objectUrl;

      const cleanup = () => {
        URL.revokeObjectURL(objectUrl);
      };

      video.addEventListener('loadedmetadata', async () => {
        const width = (video as HTMLVideoElement).videoWidth;
        const height = (video as HTMLVideoElement).videoHeight;
        const duration = (video as HTMLVideoElement).duration; // 秒

        // 时长：20s-60s
        if (duration < 20 || duration > 60) {
          message.error('视频时长需为20秒-1分钟');
          cleanup();
          resolve(false);
          return;
        }

        // 分辨率与比例：1080p 且 16:9 或 9:16
        const isLandscape1080p = width === 1920 && height === 1080; // 16:9
        const isPortrait1080p = width === 1080 && height === 1920; // 9:16
        const ratio = width / height;
        const isApprox169 = Math.abs(ratio - 16 / 9) < 0.02;
        const isApprox916 = Math.abs(ratio - 9 / 16) < 0.02;
        const isMatchResolution = isLandscape1080p || isPortrait1080p;
        const isMatchAspect = isApprox169 || isApprox916;

        if (!isMatchResolution || !isMatchAspect) {
          message.error('请上传分辨率为1080p，横版(16:9)或竖版(9:16)的视频');
          cleanup();
          resolve(false);
          return;
        }

        // 近似校验 FPS ≈ 25
        const approxFps = await new Promise<number | undefined>((res) => {
          const v: any = video as any;

          // 若支持 requestVideoFrameCallback，统计1秒内帧数
          if (typeof v.requestVideoFrameCallback === 'function') {
            let frames = 0;
            const start = performance.now();
            const sampleMs = 1000;
            const onFrame = () => {
              frames += 1;
              if (performance.now() - start < sampleMs) {
                v.requestVideoFrameCallback(onFrame);
              }
            };
            video
              .play()
              .then(() => {
                v.requestVideoFrameCallback(onFrame);
                setTimeout(() => {
                  video.pause();
                  res(frames);
                }, sampleMs + 50);
              })
              .catch(() => res(undefined));
            return;
          }

          // Fallback：使用 getVideoPlaybackQuality 估算
          const qualityApi = (video as any).getVideoPlaybackQuality?.();
          // 一些浏览器可能没有该API
          if (!qualityApi) {
            res(undefined);
            return;
          }
          const startFrames = qualityApi.totalVideoFrames || 0;
          video
            .play()
            .then(() => {
              setTimeout(() => {
                const q2 = (video as any).getVideoPlaybackQuality?.();
                video.pause();
                if (q2 && typeof q2.totalVideoFrames === 'number') {
                  res(Math.max(0, q2.totalVideoFrames - startFrames));
                } else {
                  res(undefined);
                }
              }, 1000);
            })
            .catch(() => res(undefined));
        });

        if (typeof approxFps === 'number') {
          // 容差：±1fps
          if (Math.abs(approxFps - 25) > 1) {
            message.error('请上传帧率为25FPS的视频');
            cleanup();
            resolve(false);
            return;
          }
        }

        cleanup();
        resolve(true);
      });

      video.addEventListener('error', () => {
        cleanup();
        message.error('视频文件无法读取，请重新选择');
        resolve(false);
      });
    });
  };

  const uploadProps = {
    beforeUpload: async (file: File) => {
      // 类型：MOV 或 MP4
      const isVideoType =
        ['video/mp4', 'video/quicktime'].includes(file.type) ||
        file.name.toLowerCase().endsWith('.mp4') ||
        file.name.toLowerCase().endsWith('.mov');
      if (!isVideoType) {
        message.error('请上传MOV\MP4格式的视频');
        return false;
      }

      // 大小：<= 500MB
      const maxSizeBytes = 500 * 1024 * 1024;
      if (file.size > maxSizeBytes) {
        message.error('文件大小不超过500MB');
        return false;
      }

      // 元数据校验：分辨率/比例、时长、FPS
      // const metaOk = await validateVideoMetadata(file);
      // return metaOk;
      return true;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      // 按当前后端约定使用 'video' 字段
      formData.append('video', file);

      // 创建取消令牌
      const cancelTokenSource = axios.CancelToken.source();
      uploadCancelToken.value = cancelTokenSource;

      loading.value = true;
      isUploading.value = true;
      uploadProgress.value = 0;

      try {
        // 直接使用axios避免request.ts中的问题
        const response = await axios.post(
          `${VITE_APP_AVATAR_URL}/virtual-classroom-service/face/video/upload/`,
          formData,
          {
            headers: {
              'content-type': 'multipart/form-data',
            },
            cancelToken: cancelTokenSource.token,
            onUploadProgress: (progressEvent: any) => {
              if (progressEvent.total) {
                // 文件上传进度占总进度的90%，确保有缓冲空间
                const fileUploadProgress = Math.round((progressEvent.loaded * 90) / progressEvent.total);
                uploadProgress.value = Math.min(fileUploadProgress, 90);
              }
            },
          },
        );

        const data = response.data;

        console.log(data);
        uploadResult.value = data as UploadVideoResult;
        uploadAvatarUrl.value = data.url;

        // 接口完全完成，进度达到100%
        uploadProgress.value = 100;

        // 短暂延迟后隐藏进度条
        setTimeout(() => {
          isUploading.value = false;
        }, 500);
      } catch (error: any) {
        console.log('捕获到错误:', error);
        console.log('是否为取消错误:', axios.isCancel(error));

        if (axios.isCancel(error)) {
          // 用户手动取消，不显示错误提示
          console.log('上传已取消');
        } else {
          // 真正的上传失败才显示错误提示
          console.log('上传失败，显示错误提示');
          message.error('上传失败，请检查视频是否符合要求');
        }
        isUploading.value = false;
        uploadProgress.value = 0;
      } finally {
        loading.value = false;
        uploadCancelToken.value = null;
      }
    },
    multiple: false,
    fileList: [],
    accept: 'video/mp4,video/quicktime',
    showUploadList: false,
  };

  // 打开模态框
  const handleOpenSpeakersModal = () => {
    console.log('打开选择声音模态框', speakersModalRef.value);
    speakersModalRef.value?.openSpeakersModal();
  };

  const handleCloseSpeakersModal = () => {
    console.log('关闭选择声音模态框');
  };

  // 处理声音选择
  const handleSpeakerSelected = (speaker: { name: string; description: string }) => {
    selectedSpeaker.value = speaker;
    console.log('选中的声音:', speaker);
  };

  // 处理用户同意协议
  const handleUserAgreeTerms = () => {
    isAgreedToTerms.value = true;
  };

  // 打开协议弹窗
  const handleOpenUserRightsNotice = () => {
    userRightsNoticeRef.value?.openModal();
  };

  // 预览视频点击播放/暂停支持
  const previewVideoRef = ref<HTMLVideoElement | null>(null);
  const togglePreviewVideoPlay = () => {
    const video = previewVideoRef.value;
    if (!video) return;
    if (video.paused) {
      video.play();
    } else {
      video.pause();
    }
  };

  // 删除已上传的视频
  const handleDeleteVideo = () => {
    uploadAvatarUrl.value = '';
    uploadResult.value = null;
    preViewUrl.value = '';
    isAvatarUploaded.value = true;
  };

  // 取消上传
  const handleCancelUpload = () => {
    console.log('用户点击取消上传');
    if (uploadCancelToken.value) {
      console.log('执行取消令牌');
      uploadCancelToken.value.cancel('用户取消上传');
      uploadCancelToken.value = null;
    }
    isUploading.value = false;
    uploadProgress.value = 0;
    loading.value = false;
  };
</script>

<template>
  <a-modal
    :open="isOpenModal"
    title="视频生成数字人"
    :footer="null"
    :width="1122"
    :mask-closable="false"
    centered
    :style="{ height: '800px' }"
    :body-style="{ overflow: 'hidden' }"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="create-digital-man-modal">
      <div class="create">
        <!-- <Step :step="1" title="训练方式" />
        <a-config-provider :theme="theme">
          <a-radio-group v-model:value="trainMode" class="custom-radio-group">
            <div
              v-for="{ value, title, tips, disabled } in TrainModes"
              :key="title"
              :class="{
                'radio-item': true,
                selected: trainMode === value,
                disabled: disabled,
              }"
              @click="!disabled && handleTrainModeChange(value)"
            >
              <a-radio :disabled="disabled" :value="value">
                {{ title }}
              </a-radio>
              <div class="desc">{{ tips }}</div>
            </div>
          </a-radio-group>
        </a-config-provider> -->

        <div class="custom-upload-avatar">
          <div class="flex flex-justify-start align-center">
            <!-- <Step :step="1" title="上传视频" :tooltips="UploadVideoTips" :required="true" /> -->
            <Step :step="1" title="上传视频" :required="true" />
            <a-popover placement="rightTop">
              <template #content>
                <div class="text-#17181A text-12px font-600 mb-6px">1. 人物要求</div>
                <div v-for="(text, i) in UploadVideoTips.requirement" :key="'req-' + i" class="pl-16px mb-4px">
                  <span class="text-#17181A">·</span>
                  <span class="text-#636466 text-12px ml-6px">{{ text }}</span>
                </div>

                <div class="text-#17181A text-12px font-600 mt-8px mb-6px">2. 视频内容</div>
                <div v-for="(item, i) in UploadVideoTips.content" :key="'content-' + i" class="pl-16px mb-4px">
                  <span class="text-#17181A text-12px font-600">· {{ item.key }}：</span>
                  <span class="text-#636466 text-12px">{{ item.value }}</span>
                </div>

                <div class="text-#17181A text-12px font-600 mt-8px mb-6px">3. 拍摄要求</div>
                <div v-for="(item, i) in UploadVideoTips.shoot" :key="'shoot-' + i" class="pl-16px mb-4px">
                  <span class="text-#17181A text-12px font-600">· {{ item.key }}：</span>
                  <span class="text-#636466 text-12px">{{ item.value }}</span>
                </div>
                <p class="font-400 text-12px text-#999999 leading-17px text-left mt-8px mb-6px">错误示范</p>
                <div class="grid grid-cols-4 gap-y-16px gap-x-24px pl-16px">
                  <div v-for="(item, i) in AvatarErrorPhotos" :key="i" class="flex flex-col items-center">
                    <img class="w-79px h-59px object-cover rounded-4px bg-#eaf2ff" :src="item.url" alt="错误示例" />
                    <div class="text-#17181A text-12px font-400 mt-6px">{{ item.key }}</div>
                  </div>
                </div>
              </template>
              <template #title>
                <span>人物视频拍照要求</span>
              </template>
              <InfoCircleOutlined class="text-#231F20-13px pt-6px" />
            </a-popover>
            <p class="text-12px text-#636466 font-400 text-left pt-20px">
              （请上传一段视频，作为驱动数字人的底板视频）
            </p>
          </div>

          <a-upload
            v-bind="uploadProps"
            list-type="picture-card"
            :class="{ 'video-uploader': true, 'has-video': !!uploadAvatarUrl, error: !isAvatarUploaded }"
          >
            <template #default>
              <div v-if="uploadAvatarUrl && !isUploading">
                <div class="upload-preview" style="position: relative">
                  <video
                    ref="previewVideoRef"
                    :src="uploadAvatarUrl"
                    controls
                    playsinline
                    controlslist="nodownload noplaybackrate"
                    disablepictureinpicture
                    class="avatar-icon"
                    @click="togglePreviewVideoPlay"
                  />
                  <button
                    type="button"
                    class="absolute top-8px right-8px w-24px h-24px rounded-50% bg-black/60 border-none text-white cursor-pointer flex items-center justify-center z-10"
                    @click.stop="handleDeleteVideo"
                  >
                    <DeleteOutlined style="font-size: 12px" />
                  </button>
                  <!-- <div class="upload-overlay">
                    <UploadOutlined class="re-upload-icon" />

                    重新上传
                  </div> -->
                </div>
              </div>
              <div v-else>
                <div class="upload-icon-box">
                  <button type="button" class="upload-button">
                    <template v-if="isUploading">
                      <!-- Upload progress display -->
                      <div class="upload-progress-container">
                        <!-- 视频图标 -->
                        <div class="video-upload-icon">
                          <Icon name="a-shipin2" :size="60" />
                        </div>

                        <div class="upload-progress">
                          <!-- 进度条 -->
                          <div class="upload-progress-bar">
                            <div class="progress-bg">
                              <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
                            </div>
                            <div class="progress-info">
                              <span class="progress-text">视频上传中</span>
                              <span class="progress-percent">{{ uploadProgress }}%</span>
                            </div>
                          </div>

                          <!-- 取消按钮 -->
                          <div class="cancel-upload-btn">
                            <a-button type="link" block @click.stop="handleCancelUpload"> 取消 </a-button>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-else-if="loading">
                      <LoadingOutlined />
                    </template>
                    <template v-else>
                      <PlusOutlined
                        class="upload-plus-icon"
                        style="font-size: 24px; font-weight: 600; color: #969799; margin-bottom: 12px"
                      />
                      <p v-if="isAvatarUploaded" class="upload-tip">将视频拖放此处，或点击上传</p>
                      <p v-if="isAvatarUploaded" class="upload-format-tip">
                        <span>1. 支持上传 MOV\MP4 格式的横版（16：9）或竖版（9:16）视频；</span>
                        <span>2. 视频帧率为25 fps，分辨率为1080p；</span>
                        <span>3. 视频时长为20秒~1分钟，文件大小不超过500MB；</span>
                        <span>4. 视频背景为白墙或者绿幕最佳，衣服颜色不要跟背景接近。</span>
                      </p>
                    </template>
                  </button>
                  <span v-if="!isAvatarUploaded" class="error-msg">请上传视频</span>
                </div>
              </div>
            </template>
          </a-upload>
        </div>

        <!-- <video src="https://minio-test.shukeyun.com/avatar-back-end/videos/20250813_144834_avatar-hanlei-s1.mp4" controls></video> -->

        <div class="format-tip">
          <Step :step="2" title="声音" />
          <p class="avatar-format-tip">(非必选，如设置，生成的数字人将有对应声音)</p>
        </div>

        <div class="speakers-list custom-speakers-section">
          <a-input
            block
            :class="['speakers-btn', { 'is-selected': !!selectedSpeaker }]"
            :value="selectedSpeaker?.description || ''"
            placeholder="请选择声音"
            @click="handleOpenSpeakersModal"
          >
            <template #suffix>
              <RightOutlined />
            </template>
          </a-input>
        </div>
        <a-button :disabled="pageLoading" class="compose-preview-btn" block @click="handlePreview"> 合成预览 </a-button>
      </div>

      <div class="preview">
        <template v-if="preViewUrl === ''">
          <div class="preview-init" style="position: relative; min-height: 300px">
            <template v-if="pageLoading">
              <div
                style="
                  position: absolute;
                  left: 0;
                  top: 0;
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  z-index: 10;
                  background: #f7f8fa;
                "
              >
                <a-spin :spinning="true" tip="正在生成预览，请耐心等待" class="loading-spin">
                  <template #indicator>
                    <LoadingOutlined style="font-size: 60px" spin />
                  </template>
                </a-spin>
              </div>
            </template>
            <template v-else>
              <img :src="DefaultDigitalPreviewImg" alt="" />
              <div class="preview-title">数字人预览</div>
              <div class="preview-tip">请根据左侧提示操作，生成后的数字人形象将在此预览</div>
            </template>
          </div>
        </template>
        <template v-else>
          <div class="preview-train">
            <div class="preview-content">
              <div class="mt-20px mb-13px whitespace-nowrap">
                <span class="text-14px text-#17181A font-600">数字人预览</span>
                <span class="text-12px text-#636466"
                  >（此处只做形象预览，确认生成后，可在“我的数字人”中查看动态效果）</span
                >
              </div>

              <img class="train-img" :src="preViewUrl" />
              <!-- <video class="train-img" :src="uploadAvatarUrl"/> -->
              <div class="preview-footer">
                <a-input
                  v-model="name"
                  :status="!isNameEntered ? 'error' : ''"
                  :class="{ 'preview-input': true, error: !isNameEntered }"
                  placeholder="请输入数字人名称"
                  :maxlength="10"
                  show-count
                  @change="(e: Event) => handleNameChange((e.target as HTMLInputElement).value)"
                />
                <a-button :disabled="pageLoading" type="primary" class="preview-btn" @click="handleTrain">
                  确认生成
                </a-button>
              </div>
              <!-- 版权校验勾选框 -->
              <div class="terms-agreement">
                <a-checkbox v-model:checked="isAgreedToTerms">
                  我已阅读并同意
                  <a href="javascript:void(0)" class="terms-link" @click="handleOpenUserRightsNotice">
                    《使用者承诺须知》
                  </a>
                </a-checkbox>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <digitalHumanSpeakersModal
      ref="speakersModalRef"
      :handle-close-modal="handleCloseSpeakersModal"
      @speaker-selected="handleSpeakerSelected"
    />

    <UserRightsNotice ref="userRightsNoticeRef" @agree="handleUserAgreeTerms" />
  </a-modal>
</template>

<style lang="less" src="./custom.less"></style>
<style lang="less" src="./custom-antd.less"></style>
